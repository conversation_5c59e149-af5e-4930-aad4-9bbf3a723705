class DynamicCampaigns {
  constructor() {
    this.campaigns = [];
    this.cartData = null;
    this.init();
  }

  init() {
    this.loadSettings();
    this.bindEvents();
    this.updateCampaigns();
  }

  loadSettings() {
    // Load campaign settings from theme settings
    this.settings = {
      enabled: window.themeSettings?.dynamic_campaigns_enabled !== false,
      showProgress: window.themeSettings?.show_campaign_progress !== false,
      showSuggestions: window.themeSettings?.show_campaign_suggestions !== false,
      cartCampaigns: window.themeSettings?.cart_campaigns || '',
      productCampaigns: window.themeSettings?.product_campaigns || ''
    };
  }

  bindEvents() {
    // Listen for cart updates
    document.addEventListener('cart:updated', () => {
      this.updateCampaigns();
    });

    // Listen for quantity changes
    document.addEventListener('change', (e) => {
      if (e.target.matches('.quantity__input')) {
        setTimeout(() => this.updateCampaigns(), 100);
      }
    });

    // Listen for cart changes via fetch
    const originalFetch = window.fetch;
    window.fetch = (...args) => {
      const result = originalFetch.apply(this, args);

      // Check if this is a cart-related request
      if (args[0] && (
        args[0].includes('/cart/add') ||
        args[0].includes('/cart/change') ||
        args[0].includes('/cart/update')
      )) {
        result.then(() => {
          setTimeout(() => this.updateCampaigns(), 200);
        });
      }

      return result;
    };

    // Listen for page visibility changes to refresh campaigns
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        this.updateCampaigns();
      }
    });
  }

  async updateCampaigns() {
    if (!this.settings.enabled) return;

    try {
      await this.fetchCartData();
      this.parseCampaigns();
      this.calculateActiveCampaigns();
      this.renderCampaigns();
    } catch (error) {
      console.error('Error updating campaigns:', error);
    }
  }

  async fetchCartData() {
    const response = await fetch('/cart.js');
    this.cartData = await response.json();
  }

  parseCampaigns() {
    this.campaigns = [];

    // Parse cart campaigns
    if (this.settings.cartCampaigns) {
      const cartLines = this.settings.cartCampaigns.split('\n');
      cartLines.forEach(line => {
        const campaign = this.parseCartCampaign(line.trim());
        if (campaign) this.campaigns.push(campaign);
      });
    }

    // Parse product campaigns
    if (this.settings.productCampaigns) {
      const productLines = this.settings.productCampaigns.split('\n');
      productLines.forEach(line => {
        const campaign = this.parseProductCampaign(line.trim());
        if (campaign) this.campaigns.push(campaign);
      });
    }
  }

  parseCartCampaign(line) {
    if (!line) return null;
    
    const parts = line.split('|');
    if (parts.length < 5) return null;

    return {
      type: 'cart',
      campaignType: parts[0].trim(),
      condition: parseFloat(parts[1].trim()),
      value: parseFloat(parts[2].trim()),
      title: parts[3].trim(),
      description: parts[4].trim(),
      active: false,
      progress: 0
    };
  }

  parseProductCampaign(line) {
    if (!line) return null;
    
    const parts = line.split('|');
    if (parts.length < 6) return null;

    return {
      type: 'product',
      productHandle: parts[0].trim(),
      campaignType: parts[1].trim(),
      condition: parseFloat(parts[2].trim()),
      value: parseFloat(parts[3].trim()),
      title: parts[4].trim(),
      description: parts[5].trim(),
      active: false,
      progress: 0
    };
  }

  calculateActiveCampaigns() {
    if (!this.cartData) return;

    this.campaigns.forEach(campaign => {
      if (campaign.type === 'cart') {
        this.calculateCartCampaign(campaign);
      } else if (campaign.type === 'product') {
        this.calculateProductCampaign(campaign);
      }
    });
  }

  calculateCartCampaign(campaign) {
    const cartTotal = this.cartData.total_price / 100; // Convert from cents
    const cartQuantity = this.cartData.item_count;

    switch (campaign.campaignType) {
      case 'amount_threshold':
        campaign.progress = Math.min((cartTotal / campaign.condition) * 100, 100);
        campaign.active = cartTotal >= campaign.condition;
        campaign.remaining = Math.max(campaign.condition - cartTotal, 0);
        campaign.discount = campaign.active ? campaign.value : 0;
        break;

      case 'quantity_threshold':
        campaign.progress = Math.min((cartQuantity / campaign.condition) * 100, 100);
        campaign.active = cartQuantity >= campaign.condition;
        campaign.remaining = Math.max(campaign.condition - cartQuantity, 0);
        campaign.discount = campaign.active ? campaign.value : 0;
        break;

      case 'free_shipping':
        campaign.progress = Math.min((cartTotal / campaign.condition) * 100, 100);
        campaign.active = cartTotal >= campaign.condition;
        campaign.remaining = Math.max(campaign.condition - cartTotal, 0);
        campaign.discount = campaign.active ? 'Ücretsiz Kargo' : 0;
        break;

      case 'percentage_discount':
        campaign.progress = Math.min((cartTotal / campaign.condition) * 100, 100);
        campaign.active = cartTotal >= campaign.condition;
        campaign.remaining = Math.max(campaign.condition - cartTotal, 0);
        campaign.discount = campaign.active ? `%${campaign.value} İndirim` : 0;
        break;
    }
  }

  calculateProductCampaign(campaign) {
    const productItem = this.cartData.items.find(item => 
      item.handle === campaign.productHandle
    );

    if (!productItem) {
      campaign.progress = 0;
      campaign.active = false;
      campaign.remaining = campaign.condition;
      return;
    }

    switch (campaign.campaignType) {
      case 'quantity':
        campaign.progress = Math.min((productItem.quantity / campaign.condition) * 100, 100);
        campaign.active = productItem.quantity >= campaign.condition;
        campaign.remaining = Math.max(campaign.condition - productItem.quantity, 0);
        break;
    }
  }

  renderCampaigns() {
    const container = document.querySelector('.dynamic-campaigns-container');
    if (!container) return;

    const activeCampaigns = this.campaigns.filter(c => c.active);
    const suggestedCampaigns = this.campaigns.filter(c => !c.active && c.progress > 0);

    let html = '';

    // Render active campaigns
    if (activeCampaigns.length > 0) {
      html += '<div class="campaigns-section campaigns-active">';
      html += '<h3 class="campaigns-title">🎉 Aktif Kampanyalar</h3>';
      activeCampaigns.forEach(campaign => {
        html += this.renderCampaignCard(campaign, 'active');
      });
      html += '</div>';
    }

    // Render suggested campaigns
    if (this.settings.showSuggestions && suggestedCampaigns.length > 0) {
      html += '<div class="campaigns-section campaigns-suggestions">';
      html += '<h3 class="campaigns-title">💡 Kampanya Fırsatları</h3>';
      suggestedCampaigns.forEach(campaign => {
        html += this.renderCampaignCard(campaign, 'suggestion');
      });
      html += '</div>';
    }

    container.innerHTML = html;
  }

  renderCampaignCard(campaign, type) {
    const isActive = type === 'active';
    const progressWidth = Math.min(campaign.progress, 100);

    let progressText = '';
    if (!isActive && this.settings.showProgress) {
      if (campaign.campaignType === 'amount_threshold' || campaign.campaignType === 'free_shipping' || campaign.campaignType === 'percentage_discount') {
        progressText = `${this.formatMoney(campaign.remaining * 100)} daha ekleyin`;
      } else if (campaign.campaignType === 'quantity_threshold') {
        progressText = `${campaign.remaining} ürün daha ekleyin`;
      }
    }

    let discountText = '';
    if (isActive && campaign.discount) {
      if (typeof campaign.discount === 'number') {
        discountText = `<div class="campaign-discount">💰 ${this.formatMoney(campaign.discount * 100)} tasarruf</div>`;
      } else {
        discountText = `<div class="campaign-discount">🎁 ${campaign.discount}</div>`;
      }
    }

    return `
      <div class="campaign-card campaign-card--${type}">
        <div class="campaign-content">
          <h4 class="campaign-title">${campaign.title}</h4>
          <p class="campaign-description">${campaign.description}</p>
          ${discountText}
          ${!isActive && this.settings.showProgress ? `
            <div class="campaign-progress">
              <div class="progress-bar">
                <div class="progress-fill" style="width: ${progressWidth}%"></div>
              </div>
              <span class="progress-text">${progressText}</span>
            </div>
          ` : ''}
        </div>
        <div class="campaign-badge">
          ${isActive ? '✅' : '🎯'}
        </div>
      </div>
    `;
  }

  formatMoney(cents) {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY'
    }).format(cents / 100);
  }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  window.dynamicCampaigns = new DynamicCampaigns();
});

// Export for use in other scripts
window.DynamicCampaigns = DynamicCampaigns;
