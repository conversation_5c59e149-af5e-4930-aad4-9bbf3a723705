[{"name": "theme_info", "theme_name": "Dawn", "theme_version": "15.4.0", "theme_author": "Shopify", "theme_documentation_url": "https://help.shopify.com/manual/online-store/themes", "theme_support_url": "https://support.shopify.com/"}, {"name": "t:settings_schema.logo.name", "settings": [{"type": "image_picker", "id": "logo", "label": "t:settings_schema.logo.settings.logo_image.label"}, {"type": "range", "id": "logo_width", "min": 50, "max": 300, "step": 10, "default": 100, "unit": "px", "label": "t:settings_schema.logo.settings.logo_width.label"}, {"type": "image_picker", "id": "favicon", "label": "t:settings_schema.logo.settings.favicon.label", "info": "t:settings_schema.logo.settings.favicon.info"}]}, {"name": "t:settings_schema.colors.name", "settings": [{"type": "color_scheme_group", "id": "color_schemes", "definition": [{"type": "color", "id": "background", "label": "t:settings_schema.colors.settings.background.label", "default": "#FFFFFF"}, {"type": "color_background", "id": "background_gradient", "label": "t:settings_schema.colors.settings.background_gradient.label", "info": "t:settings_schema.colors.settings.background_gradient.info"}, {"type": "color", "id": "text", "label": "t:settings_schema.colors.settings.text.label", "default": "#121212"}, {"type": "color", "id": "button", "label": "t:settings_schema.colors.settings.button_background.label", "default": "#121212"}, {"type": "color", "id": "button_label", "label": "t:settings_schema.colors.settings.button_label.label", "default": "#FFFFFF"}, {"type": "color", "id": "secondary_button_label", "label": "t:settings_schema.colors.settings.secondary_button_label.label", "default": "#121212"}, {"type": "color", "id": "shadow", "label": "t:settings_schema.colors.settings.shadow.label", "default": "#121212"}], "role": {"text": "text", "background": {"solid": "background", "gradient": "background_gradient"}, "links": "secondary_button_label", "icons": "text", "primary_button": "button", "on_primary_button": "button_label", "primary_button_border": "button", "secondary_button": "background", "on_secondary_button": "secondary_button_label", "secondary_button_border": "secondary_button_label"}}]}, {"name": "t:settings_schema.typography.name", "settings": [{"type": "header", "content": "t:settings_schema.typography.settings.header__1.content"}, {"type": "font_picker", "id": "type_header_font", "default": "assistant_n4", "label": "t:settings_schema.typography.settings.type_header_font.label"}, {"type": "range", "id": "heading_scale", "min": 100, "max": 150, "step": 5, "unit": "%", "label": "t:settings_schema.typography.settings.heading_scale.label", "default": 100}, {"type": "header", "content": "t:settings_schema.typography.settings.header__2.content"}, {"type": "font_picker", "id": "type_body_font", "default": "assistant_n4", "label": "t:settings_schema.typography.settings.type_body_font.label"}, {"type": "range", "id": "body_scale", "min": 100, "max": 130, "step": 5, "unit": "%", "label": "t:settings_schema.typography.settings.body_scale.label", "default": 100}]}, {"name": "t:settings_schema.layout.name", "settings": [{"type": "range", "id": "page_width", "min": 1000, "max": 1600, "step": 100, "default": 1200, "unit": "px", "label": "t:settings_schema.layout.settings.page_width.label"}, {"type": "range", "id": "spacing_sections", "min": 0, "max": 100, "step": 4, "unit": "px", "label": "t:settings_schema.layout.settings.spacing_sections.label", "default": 0}, {"type": "header", "content": "t:settings_schema.layout.settings.header__grid.content"}, {"type": "paragraph", "content": "t:settings_schema.layout.settings.paragraph__grid.content"}, {"type": "range", "id": "spacing_grid_horizontal", "min": 4, "max": 40, "step": 4, "default": 8, "unit": "px", "label": "t:settings_schema.layout.settings.spacing_grid_horizontal.label"}, {"type": "range", "id": "spacing_grid_vertical", "min": 4, "max": 40, "step": 4, "default": 8, "unit": "px", "label": "t:settings_schema.layout.settings.spacing_grid_vertical.label"}]}, {"name": "t:settings_schema.animations.name", "settings": [{"type": "checkbox", "id": "animations_reveal_on_scroll", "label": "t:settings_schema.animations.settings.animations_reveal_on_scroll.label", "default": true}, {"type": "select", "id": "animations_hover_elements", "options": [{"value": "default", "label": "t:settings_schema.animations.settings.animations_hover_elements.options__1.label"}, {"value": "vertical-lift", "label": "t:settings_schema.animations.settings.animations_hover_elements.options__2.label"}, {"value": "3d-lift", "label": "t:settings_schema.animations.settings.animations_hover_elements.options__3.label"}], "default": "default", "label": "t:settings_schema.animations.settings.animations_hover_elements.label", "info": "t:settings_schema.animations.settings.animations_hover_elements.info"}]}, {"name": "t:settings_schema.buttons.name", "settings": [{"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "buttons_border_thickness", "min": 0, "max": 12, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 1}, {"type": "range", "id": "buttons_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 100}, {"type": "range", "id": "buttons_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 0}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "buttons_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "buttons_shadow_horizontal_offset", "min": -12, "max": 12, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "buttons_shadow_vertical_offset", "min": -12, "max": 12, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "buttons_shadow_blur", "min": 0, "max": 20, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}]}, {"name": "t:settings_schema.variant_pills.name", "settings": [{"type": "paragraph", "content": "t:settings_schema.variant_pills.paragraph"}, {"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "variant_pills_border_thickness", "min": 0, "max": 12, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 1}, {"type": "range", "id": "variant_pills_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 55}, {"type": "range", "id": "variant_pills_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 40}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "variant_pills_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "variant_pills_shadow_horizontal_offset", "min": -12, "max": 12, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "variant_pills_shadow_vertical_offset", "min": -12, "max": 12, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "variant_pills_shadow_blur", "min": 0, "max": 20, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}]}, {"name": "t:settings_schema.inputs.name", "settings": [{"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "inputs_border_thickness", "min": 0, "max": 12, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 1}, {"type": "range", "id": "inputs_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 55}, {"type": "range", "id": "inputs_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 0}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "inputs_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "inputs_shadow_horizontal_offset", "min": -12, "max": 12, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "inputs_shadow_vertical_offset", "min": -12, "max": 12, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "inputs_shadow_blur", "min": 0, "max": 20, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}]}, {"name": "t:settings_schema.cards.name", "settings": [{"type": "select", "id": "card_style", "options": [{"value": "standard", "label": "t:settings_schema.cards.settings.style.options__1.label"}, {"value": "card", "label": "t:settings_schema.cards.settings.style.options__2.label"}], "default": "standard", "label": "t:settings_schema.cards.settings.style.label"}, {"type": "range", "id": "card_image_padding", "min": 0, "max": 20, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.image_padding.label", "default": 0}, {"type": "select", "id": "card_text_alignment", "options": [{"value": "left", "label": "t:settings_schema.global.settings.text_alignment.options__1.label"}, {"value": "center", "label": "t:settings_schema.global.settings.text_alignment.options__2.label"}, {"value": "right", "label": "t:settings_schema.global.settings.text_alignment.options__3.label"}], "default": "left", "label": "t:settings_schema.global.settings.text_alignment.label"}, {"type": "color_scheme", "id": "card_color_scheme", "label": "t:sections.all.colors.label", "default": "scheme-2"}, {"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "card_border_thickness", "min": 0, "max": 24, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 0}, {"type": "range", "id": "card_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "card_corner_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 0}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "card_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 10}, {"type": "range", "id": "card_shadow_horizontal_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "card_shadow_vertical_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "card_shadow_blur", "min": 0, "max": 40, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}]}, {"name": "t:settings_schema.collection_cards.name", "settings": [{"type": "select", "id": "collection_card_style", "options": [{"value": "standard", "label": "t:settings_schema.collection_cards.settings.style.options__1.label"}, {"value": "card", "label": "t:settings_schema.collection_cards.settings.style.options__2.label"}], "default": "standard", "label": "t:settings_schema.collection_cards.settings.style.label"}, {"type": "range", "id": "collection_card_image_padding", "min": 0, "max": 20, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.image_padding.label", "default": 0}, {"type": "select", "id": "collection_card_text_alignment", "options": [{"value": "left", "label": "t:settings_schema.global.settings.text_alignment.options__1.label"}, {"value": "center", "label": "t:settings_schema.global.settings.text_alignment.options__2.label"}, {"value": "right", "label": "t:settings_schema.global.settings.text_alignment.options__3.label"}], "default": "left", "label": "t:settings_schema.global.settings.text_alignment.label"}, {"type": "color_scheme", "id": "collection_card_color_scheme", "label": "t:sections.all.colors.label", "default": "scheme-2"}, {"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "collection_card_border_thickness", "min": 0, "max": 24, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 0}, {"type": "range", "id": "collection_card_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "collection_card_corner_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 0}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "collection_card_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 10}, {"type": "range", "id": "collection_card_shadow_horizontal_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "collection_card_shadow_vertical_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "collection_card_shadow_blur", "min": 0, "max": 40, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}]}, {"name": "t:settings_schema.blog_cards.name", "settings": [{"type": "select", "id": "blog_card_style", "options": [{"value": "standard", "label": "t:settings_schema.blog_cards.settings.style.options__1.label"}, {"value": "card", "label": "t:settings_schema.blog_cards.settings.style.options__2.label"}], "default": "standard", "label": "t:settings_schema.blog_cards.settings.style.label"}, {"type": "range", "id": "blog_card_image_padding", "min": 0, "max": 20, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.image_padding.label", "default": 0}, {"type": "select", "id": "blog_card_text_alignment", "options": [{"value": "left", "label": "t:settings_schema.global.settings.text_alignment.options__1.label"}, {"value": "center", "label": "t:settings_schema.global.settings.text_alignment.options__2.label"}, {"value": "right", "label": "t:settings_schema.global.settings.text_alignment.options__3.label"}], "default": "left", "label": "t:settings_schema.global.settings.text_alignment.label"}, {"type": "color_scheme", "id": "blog_card_color_scheme", "label": "t:sections.all.colors.label", "default": "scheme-2"}, {"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "blog_card_border_thickness", "min": 0, "max": 24, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 0}, {"type": "range", "id": "blog_card_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "blog_card_corner_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 0}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "blog_card_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 10}, {"type": "range", "id": "blog_card_shadow_horizontal_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "blog_card_shadow_vertical_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "blog_card_shadow_blur", "min": 0, "max": 40, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}]}, {"name": "t:settings_schema.content_containers.name", "settings": [{"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "text_boxes_border_thickness", "min": 0, "max": 24, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 0}, {"type": "range", "id": "text_boxes_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "text_boxes_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 0}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "text_boxes_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "text_boxes_shadow_horizontal_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "text_boxes_shadow_vertical_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "text_boxes_shadow_blur", "min": 0, "max": 40, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}]}, {"name": "t:settings_schema.media.name", "settings": [{"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "media_border_thickness", "min": 0, "max": 24, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 1}, {"type": "range", "id": "media_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 5}, {"type": "range", "id": "media_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 0}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "media_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "media_shadow_horizontal_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "media_shadow_vertical_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "media_shadow_blur", "min": 0, "max": 40, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}]}, {"name": "t:settings_schema.popups.name", "settings": [{"type": "paragraph", "content": "t:settings_schema.popups.paragraph"}, {"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "popup_border_thickness", "min": 0, "max": 24, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 1}, {"type": "range", "id": "popup_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 10}, {"type": "range", "id": "popup_corner_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 0}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "popup_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "popup_shadow_horizontal_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "popup_shadow_vertical_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "popup_shadow_blur", "min": 0, "max": 40, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}]}, {"name": "t:settings_schema.drawers.name", "settings": [{"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "drawer_border_thickness", "min": 0, "max": 24, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 1}, {"type": "range", "id": "drawer_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 10}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "drawer_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "drawer_shadow_horizontal_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "drawer_shadow_vertical_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "drawer_shadow_blur", "min": 0, "max": 40, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}]}, {"name": "t:settings_schema.badges.name", "settings": [{"type": "select", "id": "badge_position", "options": [{"value": "bottom left", "label": "t:settings_schema.badges.settings.position.options__1.label"}, {"value": "bottom right", "label": "t:settings_schema.badges.settings.position.options__2.label"}, {"value": "top left", "label": "t:settings_schema.badges.settings.position.options__3.label"}, {"value": "top right", "label": "t:settings_schema.badges.settings.position.options__4.label"}], "default": "bottom left", "label": "t:settings_schema.badges.settings.position.label"}, {"type": "range", "id": "badge_corner_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 40}, {"type": "color_scheme", "id": "sale_badge_color_scheme", "label": "t:settings_schema.badges.settings.sale_badge_color_scheme.label", "default": "scheme-5"}, {"type": "color_scheme", "id": "sold_out_badge_color_scheme", "label": "t:settings_schema.badges.settings.sold_out_badge_color_scheme.label", "default": "scheme-3"}]}, {"name": "t:settings_schema.brand_information.name", "settings": [{"type": "paragraph", "content": "t:settings_schema.brand_information.settings.paragraph.content"}, {"type": "inline_richtext", "id": "brand_headline", "label": "t:settings_schema.brand_information.settings.brand_headline.label"}, {"type": "richtext", "id": "brand_description", "label": "t:settings_schema.brand_information.settings.brand_description.label"}, {"type": "image_picker", "id": "brand_image", "label": "t:settings_schema.brand_information.settings.brand_image.label"}, {"type": "range", "id": "brand_image_width", "min": 50, "max": 550, "step": 5, "default": 100, "unit": "px", "label": "t:settings_schema.brand_information.settings.brand_image_width.label"}]}, {"name": "t:settings_schema.social-media.name", "settings": [{"type": "text", "id": "social_facebook_link", "label": "t:settings_schema.social-media.settings.social_facebook_link.label", "placeholder": "t:settings_schema.social-media.settings.social_facebook_link.info"}, {"type": "text", "id": "social_instagram_link", "label": "t:settings_schema.social-media.settings.social_instagram_link.label", "placeholder": "t:settings_schema.social-media.settings.social_instagram_link.info"}, {"type": "text", "id": "social_youtube_link", "label": "t:settings_schema.social-media.settings.social_youtube_link.label", "placeholder": "t:settings_schema.social-media.settings.social_youtube_link.info"}, {"type": "text", "id": "social_tiktok_link", "label": "t:settings_schema.social-media.settings.social_tiktok_link.label", "placeholder": "t:settings_schema.social-media.settings.social_tiktok_link.info"}, {"type": "text", "id": "social_twitter_link", "label": "t:settings_schema.social-media.settings.social_twitter_link.label", "placeholder": "t:settings_schema.social-media.settings.social_twitter_link.info"}, {"type": "text", "id": "social_snapchat_link", "label": "t:settings_schema.social-media.settings.social_snapchat_link.label", "placeholder": "t:settings_schema.social-media.settings.social_snapchat_link.info"}, {"type": "text", "id": "social_pinterest_link", "label": "t:settings_schema.social-media.settings.social_pinterest_link.label", "placeholder": "t:settings_schema.social-media.settings.social_pinterest_link.info"}, {"type": "text", "id": "social_tumblr_link", "label": "t:settings_schema.social-media.settings.social_tumblr_link.label", "placeholder": "t:settings_schema.social-media.settings.social_tumblr_link.info"}, {"type": "text", "id": "social_vimeo_link", "label": "t:settings_schema.social-media.settings.social_vimeo_link.label", "placeholder": "t:settings_schema.social-media.settings.social_vimeo_link.info"}]}, {"name": "t:settings_schema.search_input.name", "settings": [{"type": "checkbox", "id": "predictive_search_enabled", "default": true, "label": "t:settings_schema.search_input.settings.predictive_search_enabled.label"}, {"type": "checkbox", "id": "predictive_search_show_vendor", "default": false, "label": "t:settings_schema.search_input.settings.predictive_search_show_vendor.label", "info": "t:settings_schema.search_input.settings.predictive_search_show_vendor.info"}, {"type": "checkbox", "id": "predictive_search_show_price", "default": false, "label": "t:settings_schema.search_input.settings.predictive_search_show_price.label", "info": "t:settings_schema.search_input.settings.predictive_search_show_price.info"}]}, {"name": "t:settings_schema.currency_format.name", "settings": [{"type": "paragraph", "content": "t:settings_schema.currency_format.settings.paragraph"}, {"type": "checkbox", "id": "currency_code_enabled", "label": "t:settings_schema.currency_format.settings.currency_code_enabled.label", "default": true}]}, {"name": "t:settings_schema.cart.name", "settings": [{"type": "select", "id": "cart_type", "options": [{"value": "drawer", "label": "t:settings_schema.cart.settings.cart_type.drawer.label"}, {"value": "page", "label": "t:settings_schema.cart.settings.cart_type.page.label"}, {"value": "notification", "label": "t:settings_schema.cart.settings.cart_type.notification.label"}], "default": "notification", "label": "t:settings_schema.cart.settings.cart_type.label"}, {"type": "checkbox", "id": "show_vendor", "label": "t:settings_schema.cart.settings.show_vendor.label", "default": false}, {"type": "checkbox", "id": "show_cart_note", "label": "t:settings_schema.cart.settings.show_cart_note.label", "default": false}, {"type": "header", "content": "t:settings_schema.cart.settings.cart_drawer.header"}, {"type": "collection", "id": "cart_drawer_collection", "label": "t:settings_schema.cart.settings.cart_drawer.collection.label", "info": "t:settings_schema.cart.settings.cart_drawer.collection.info"}, {"type": "color_scheme", "id": "cart_color_scheme", "label": "t:sections.all.colors.label", "default": "scheme-1"}]}, {"name": "<PERSON><PERSON><PERSON>", "settings": [{"type": "header", "content": "<PERSON><PERSON><PERSON>"}, {"type": "checkbox", "id": "quantity_discount_enabled", "label": "<PERSON><PERSON><PERSON><PERSON>", "default": true}, {"type": "text", "id": "quantity_discount_default_heading", "label": "Varsayılan başlık", "default": "<PERSON><PERSON><PERSON>"}, {"type": "textarea", "id": "quantity_discount_global_tiers", "label": "Global İndirim Seviyeleri", "info": "<PERSON>üm ürünler i<PERSON>in <PERSON>lan indirim seviyeleri. Her satıra bir seviye yazın. Format: miktar|indirim_yüzdesi|etiket", "default": "2|1080|2'li <PERSON> - %10 İndirim\n3|15|3'l<PERSON> - %15 İndirim\n5|1530|5'li <PERSON> - %20 İndirim"}, {"type": "checkbox", "id": "quantity_discount_show_savings", "label": "Tasar<PERSON>f mi<PERSON>ar<PERSON> göster", "default": true}]}, {"name": "<PERSON><PERSON><PERSON>", "settings": [{"type": "header", "content": "<PERSON><PERSON>"}, {"type": "checkbox", "id": "dynamic_campaigns_enabled", "label": "Dinamik kampanyaları etkinleştir", "default": true}, {"type": "text", "id": "campaigns_heading", "label": "Kampanyalar başlığı", "default": "<PERSON><PERSON><PERSON>"}, {"type": "textarea", "id": "cart_campaigns", "label": "Sepet Kampanyaları", "info": "Her satıra bir kampanya yazın. Format: tip|koşul|değer|başlık|açıklama\nTipler: amount_threshold (tutar indirimi), quantity_threshold (adet indirimi), free_shipping (ücretsiz kargo), percentage_discount (yüzde indirimi)\nÖrnek: amount_threshold|500|50|500 TL Üzeri 50 TL İndirim|500 TL ve üzeri alışverişlerde 50 TL indirim", "default": "amount_threshold|500|50|500 TL Üzeri 50 TL İndirim|500 TL ve üzeri alışverişlerde 50 TL indirim\nquantity_threshold|3|15|3 Ürün Al 15 TL İndirim|3 veya daha fazla ürün alımında 15 TL indirim\nfree_shipping|750|0|750 TL Üzeri Ücretsiz Kargo|750 TL ve üzeri alışverişlerde ücretsiz kargo\npercentage_discount|1000|10|1000 TL Üzeri %10 İndirim|1000 TL ve üzeri alışverişlerde %10 indirim"}, {"type": "textarea", "id": "product_campaigns", "label": "Ürün Bazlı Kampanyalar", "info": "Her satıra bir kampanya yazın. Format: product_handle|tip|koşul|değer|başlık|açıklama\nTipler: quantity (miktar indirimi), bulk_discount (toplu alım)\nÖrnek: gripball-parmak-guclendirici-stres-azaltici-silikon-egzersiz-topu-ergonomik-tasarim|quantity|2|20|2'li Paket - %20 İndirim|2 adet alımında %20 indirim", "default": "gripball-parmak-guclendirici-stres-azaltici-silikon-egzersiz-topu-ergonomik-tasarim|quantity|2|20|2'li <PERSON> - %20 İndirim|2 adet alımında %20 indirim - 240₺ tasarruf\ngripball-parmak-guclendirici-stres-azaltici-silikon-egzersiz-topu-ergonomik-tasarim|quantity|3|25|3'lü Paket - %25 İndirim|3 adet alımında %25 indirim - 450₺ tasarruf"}, {"type": "checkbox", "id": "show_campaign_progress", "label": "Kampanya ilerlemesini göster", "info": "Kullanıcının kampanyaya ne kadar yakın olduğunu gö<PERSON>ir", "default": true}, {"type": "checkbox", "id": "show_campaign_suggestions", "label": "Kampanya önerilerini göster", "info": "Kullanıcıya hangi kampanyaları kazanabileceğini gösterir", "default": true}]}]